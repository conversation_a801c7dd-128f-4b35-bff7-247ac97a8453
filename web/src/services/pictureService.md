# Picture Service API 使用说明

## uploadBaseSpuPicture 方法

### 功能描述
串行上传多张 SPU 图片，每张图片单独调用上传接口，收集所有上传结果。

### 方法签名
```typescript
uploadBaseSpuPicture(params: SpuPictureUploadVO[]): Promise<PictureUploadResult[]>
```

### 参数说明
- `params`: `SpuPictureUploadVO[]` - 要上传的图片信息数组

### 返回值
返回 `Promise<PictureUploadResult[]>`，其中 `PictureUploadResult` 包含：
- `success: boolean` - 上传是否成功
- `data?: any` - 成功时的响应数据
- `error?: any` - 失败时的错误信息
- `param: SpuPictureUploadVO` - 原始上传参数

### 使用示例

```typescript
import { uploadBaseSpuPicture } from '../services/pictureService';
import { SpuPictureUploadVO } from '../interfaces';

const uploadPictures = async () => {
  const pictures: SpuPictureUploadVO[] = [
    {
      channelProductId: 'channel-001',
      picName: 'product1.jpg',
      picSize: 1024,
      picType: 0, // 普通底图
      picUrl: 'https://example.com/product1.jpg',
      spuId: 123,
      teamId: 2 // 淘系
    },
    {
      channelProductId: 'channel-002',
      picName: 'product2.jpg',
      picSize: 2048,
      picType: 1, // 日常
      picUrl: 'https://example.com/product2.jpg',
      spuId: 456,
      teamId: 3 // 京东综合
    }
  ];

  try {
    const results = await uploadBaseSpuPicture(pictures);
    
    // 统计上传结果
    const successCount = results.filter(result => result.success).length;
    const failCount = results.filter(result => !result.success).length;
    
    console.log(`上传完成：成功 ${successCount} 张，失败 ${failCount} 张`);
    
    // 处理失败的上传
    const failedUploads = results.filter(result => !result.success);
    if (failedUploads.length > 0) {
      console.error('失败的上传:', failedUploads);
      // 可以选择重试或显示错误信息
    }
    
    // 处理成功的上传
    const successfulUploads = results.filter(result => result.success);
    successfulUploads.forEach(result => {
      console.log('上传成功:', result.param.picName, result.data);
    });
    
  } catch (error) {
    console.error('上传过程中发生意外错误:', error);
  }
};
```

### 特性
1. **串行执行**: 图片按顺序逐个上传，不会并发执行
2. **错误处理**: 单个图片上传失败不会中断整个流程
3. **结果收集**: 收集每次上传的结果，包括成功和失败的情况
4. **类型安全**: 使用 TypeScript 接口确保类型安全

### 注意事项
1. 方法会等待所有图片上传完成后才返回结果
2. 即使某些图片上传失败，方法也会继续处理剩余图片
3. 建议在调用前验证图片数据的完整性
4. 对于大量图片上传，考虑添加进度回调或分批处理
