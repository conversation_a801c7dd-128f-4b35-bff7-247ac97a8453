import { PlainObject } from '@shark/core';
import { axiosService } from '@sharkr/request';
import { BasePicQueryVO, PageVO, BasePictureListBO, SkuPictureUploadVO, SpuPictureUploadVO, PictureUploadResult } from '../interfaces';

let ApiHost = '/xhr/returnFirst';
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
    ApiHost = '';
}

// 获取底图列表
export const getBasePictureList = (params?: BasePicQueryVO): Promise<any> =>
    axiosService.postByJson(`${ApiHost}/xhr/base/picture/spu/list`, params);

// 上传底图
export const uploadBaseSpuPicture = async (params: SpuPictureUploadVO[]): Promise<PictureUploadResult[]> => {
  const results: PictureUploadResult[] = [];

  for (const param of params) {
    try {
      const result = await axiosService.postBy<PERSON>son(`${ApiHost}/xhr/base/picture/spu/upload`, param);
      results.push({
        success: true,
        data: result,
        param: param
      });
    } catch (error) {
      results.push({
        success: false,
        error: error,
        param: param
      });
    }
  }

  return results;
}

// 上传SKU图
export const uploadBaseSkuPicture = async (params: SkuPictureUploadVO[]): Promise<PictureUploadResult[]> => {
  const results: PictureUploadResult[] = []

  for (const param of params) {
    try {
      const result = await axiosService.postByJson(
        `${ApiHost}/xhr/base/picture/sku/upload`,
        param
      )
      results.push({
        success: true,
        data: result,
        param: param
      })
    } catch (error) {
      results.push({
        success: false,
        error: error,
        param: param
      })
    }
  }

  return results
}