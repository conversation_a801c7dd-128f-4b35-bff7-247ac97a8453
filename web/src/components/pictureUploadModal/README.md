# PictureUploadModal 图片上传弹窗组件

## 功能描述

PictureUploadModal 是一个专门用于图片上传的弹窗组件，支持多种图片类型的验证和批量上传功能。

## 主要功能

1. **表单配置**
   - 归属渠道选择：淘系、京东综合
   - 图片类型选择：商品主图、SKU透底图

2. **图片验证**
   - **商品主图验证**：
     - 命名规则：`严选SPUID_渠道商品ID` 或 `严选SPUID_渠道商品ID_数字`
     - 图片格式：jpg、png
     - 图片尺寸：750*1000 / 800*800 / 800*1200
   
   - **SKU透底图验证**：
     - 命名规则：
       - 通用：`严选SKUID` 或 `严选SKUID_数字`
       - 淘系专属：`严选SKUID_tb` 或 `严选SKUID_tb_数字`
       - 京东综合：`严选SKUID_jd` 或 `严选SKUID_jd_数字`
     - 图片格式：jpg、png

3. **上传结果展示**
   - 显示所有上传的图片（成功和失败）
   - 展示具体的错误原因
   - 支持删除单个结果项

## 使用方法

### 基本用法

```tsx
import React, { useState } from 'react'
import { Button } from 'antd'
import { PictureUploadModal, UploadResult } from '@/components'

const MyComponent: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false)

  const handleUploadComplete = (results: UploadResult[]) => {
    console.log('上传结果:', results)
    // 处理上传结果
  }

  return (
    <div>
      <Button onClick={() => setModalVisible(true)}>
        打开上传弹窗
      </Button>
      
      <PictureUploadModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={handleUploadComplete}
      />
    </div>
  )
}
```

## API

### PictureUploadModalProps

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| visible | 弹窗是否可见 | boolean | false | 是 |
| onCancel | 取消回调 | () => void | - | 是 |
| onOk | 确定回调，返回上传结果 | (results: UploadResult[]) => void | - | 否 |

### UploadResult

| 字段 | 说明 | 类型 |
|------|------|------|
| id | 文件唯一标识 | string |
| fileName | 文件名 | string |
| fileUrl | 上传成功后的文件URL | string \| undefined |
| channel | 归属渠道 | 'taobao' \| 'jd' |
| imageType | 图片类型 | 'main' \| 'sku' |
| status | 上传状态 | 'success' \| 'error' |
| errorMessage | 错误信息 | string \| undefined |

## 验证规则详解

### 商品主图验证规则

1. **文件名格式**：
   - 基本格式：`严选SPUID_渠道商品ID.jpg`
   - 多图格式：`严选SPUID_渠道商品ID_1.jpg`、`严选SPUID_渠道商品ID_2.jpg`
   - 示例：`YX123456_TB789012.jpg`、`YX123456_TB789012_1.png`

2. **图片尺寸**：
   - 750px × 1000px
   - 800px × 800px  
   - 800px × 1200px

3. **文件格式**：jpg、jpeg、png

### SKU透底图验证规则

1. **文件名格式**：
   - 通用格式：`严选SKUID.jpg` 或 `严选SKUID_1.jpg`
   - 淘系专属：`严选SKUID_tb.jpg` 或 `严选SKUID_tb_1.jpg`
   - 京东综合：`严选SKUID_jd.jpg` 或 `严选SKUID_jd_1.jpg`
   - 示例：`SKU123456.jpg`、`SKU123456_tb_1.png`、`SKU123456_jd.jpg`

2. **文件格式**：jpg、jpeg、png

## 注意事项

1. 组件会自动根据选择的渠道和图片类型进行相应的验证
2. 验证失败的图片也会显示在结果列表中，并显示具体的错误原因
3. 上传过程中会显示加载状态，防止重复提交
4. 组件使用了项目中现有的 `uploadFile` 服务进行文件上传
5. 弹窗关闭时会自动重置所有状态

## 样式定制

组件提供了 SCSS 样式文件，可以根据需要进行样式定制：

```scss
.picture-upload-modal {
  // 自定义样式
}
```
