import React, { useState } from 'react'
import { Button, message } from 'antd'
import { PictureUploadModal, UploadResult } from './index'

/**
 * PictureUploadModal 使用示例
 */
const PictureUploadModalExample: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false)

  const handleOpenModal = () => {
    setModalVisible(true)
  }

  const handleCloseModal = () => {
    setModalVisible(false)
  }

  const handleUploadComplete = (results: UploadResult[]) => {
    console.log('上传结果:', results)
    
    const successCount = results.filter(r => r.status === 'success').length
    const errorCount = results.filter(r => r.status === 'error').length
    
    message.success(`上传完成！成功：${successCount}个，失败：${errorCount}个`)
    
    // 这里可以处理上传结果，比如更新页面数据等
    // 成功的文件可以通过 results.filter(r => r.status === 'success') 获取
  }

  return (
    <div>
      <Button type="primary" onClick={handleOpenModal}>
        打开图片上传弹窗
      </Button>
      
      <PictureUploadModal
        visible={modalVisible}
        onCancel={handleCloseModal}
        onOk={handleUploadComplete}
      />
    </div>
  )
}

export default PictureUploadModalExample
