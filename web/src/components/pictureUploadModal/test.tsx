import React, { useState } from 'react'
import { Button, Card, Space, Typography } from 'antd'
import { PictureUploadModal, UploadResult } from './index'

const { Title, Paragraph, Text } = Typography

/**
 * PictureUploadModal 测试页面
 * 用于测试组件的各种功能
 */
const PictureUploadModalTest: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false)
  const [lastResults, setLastResults] = useState<UploadResult[]>([])

  const handleOpenModal = () => {
    setModalVisible(true)
  }

  const handleCloseModal = () => {
    setModalVisible(false)
  }

  const handleUploadComplete = (results: UploadResult[]) => {
    console.log('上传结果:', results)
    setLastResults(results)
    
    const successCount = results.filter(r => r.status === 'success').length
    const errorCount = results.filter(r => r.status === 'error').length
    
    console.log(`上传完成！成功：${successCount}个，失败：${errorCount}个`)
  }

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>PictureUploadModal 测试页面</Title>
      
      <Card title="功能测试" style={{ marginBottom: 16 }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <Button type="primary" onClick={handleOpenModal}>
            打开图片上传弹窗
          </Button>
          
          {lastResults.length > 0 && (
            <div>
              <Title level={4}>最近一次上传结果：</Title>
              <ul>
                {lastResults.map((result, index) => (
                  <li key={index}>
                    <Text strong>{result.fileName}</Text> - 
                    <Text type={result.status === 'success' ? 'success' : 'danger'}>
                      {result.status === 'success' ? '成功' : '失败'}
                    </Text>
                    {result.errorMessage && (
                      <Text type="danger"> ({result.errorMessage})</Text>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </Space>
      </Card>

      <Card title="测试用例" style={{ marginBottom: 16 }}>
        <Title level={4}>建议测试的文件名：</Title>
        
        <Paragraph>
          <Text strong>商品主图测试（选择淘系渠道）：</Text>
          <ul>
            <li>✅ 正确：YX123456_TB789012.jpg (800x800像素)</li>
            <li>✅ 正确：YX123456_TB789012_1.png (750x1000像素)</li>
            <li>❌ 错误：YX123456.jpg (缺少渠道商品ID)</li>
            <li>❌ 错误：YX123456_TB789012.gif (格式不支持)</li>
            <li>❌ 错误：YX123456_TB789012.jpg (尺寸不符合要求)</li>
          </ul>
        </Paragraph>

        <Paragraph>
          <Text strong>SKU透底图测试（选择淘系渠道）：</Text>
          <ul>
            <li>✅ 正确：SKU123456_tb.jpg</li>
            <li>✅ 正确：SKU123456_tb_1.png</li>
            <li>❌ 错误：SKU123456.jpg (淘系渠道需要_tb后缀)</li>
            <li>❌ 错误：SKU123456_jd.jpg (渠道后缀不匹配)</li>
          </ul>
        </Paragraph>

        <Paragraph>
          <Text strong>SKU透底图测试（选择京东综合渠道）：</Text>
          <ul>
            <li>✅ 正确：SKU123456_jd.jpg</li>
            <li>✅ 正确：SKU123456_jd_2.png</li>
            <li>❌ 错误：SKU123456_tb.jpg (渠道后缀不匹配)</li>
          </ul>
        </Paragraph>
      </Card>

      <Card title="使用说明">
        <Paragraph>
          1. 点击"打开图片上传弹窗"按钮<br/>
          2. 选择归属渠道（淘系/京东综合）<br/>
          3. 选择图片类型（商品主图/SKU透底图）<br/>
          4. 查看动态更新的命名规则提示<br/>
          5. 选择符合命名规则的图片文件<br/>
          6. 点击"开始上传"进行批量上传<br/>
          7. 查看上传结果列表，包含成功和失败的详细信息<br/>
          8. 可以删除单个结果项<br/>
          9. 点击"确定"完成操作
        </Paragraph>
      </Card>
      
      <PictureUploadModal
        visible={modalVisible}
        onCancel={handleCloseModal}
        onOk={handleUploadComplete}
      />
    </div>
  )
}

export default PictureUploadModalTest
