import React, { useState, useEffect } from 'react'
import { Modal, Form, Select, Upload, Button, Table, message, Space, Tag } from 'antd'
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons'
import { uploadFile } from '../../services/uploadService'
import './PictureUploadModal.scss'

const { Option } = Select

export interface PictureUploadModalProps {
  visible: boolean
  onCancel: () => void
  onOk?: (results: UploadResult[]) => void
}

export interface UploadResult {
  id: string
  fileName: string
  fileUrl?: string
  channel: string
  imageType: string
  status: 'success' | 'error'
  errorMessage?: string
}

interface ValidationResult {
  isValid: boolean
  errorMessage?: string
}

const PictureUploadModal: React.FC<PictureUploadModalProps> = ({
  visible,
  onCancel,
  onOk
}) => {
  const [form] = Form.useForm()
  const [fileList, setFileList] = useState<any[]>([])
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([])
  const [uploading, setUploading] = useState(false)

  // 监听表单值变化
  const watchedValues = Form.useWatch([], form)

  // 获取命名规则提示
  const getNamingRuleTip = () => {
    const { channel, imageType } = watchedValues || {}

    if (imageType === 'main') {
      return '严选SPUID_渠道商品ID.jpg 或 严选SPUID_渠道商品ID_数字.jpg'
    } else if (imageType === 'sku') {
      if (channel === 'taobao') {
        return '严选SKUID_tb.jpg 或 严选SKUID_tb_数字.jpg'
      } else if (channel === 'jd') {
        return '严选SKUID_jd.jpg 或 严选SKUID_jd_数字.jpg'
      } else {
        return '严选SKUID.jpg 或 严选SKUID_数字.jpg'
      }
    }
    return '请先选择图片类型和归属渠道'
  }

  // 重置状态
  const resetState = () => {
    form.resetFields()
    setFileList([])
    setUploadResults([])
    setUploading(false)
  }

  // 图片尺寸验证
  const validateImageDimensions = (file: File, imageType: string): Promise<ValidationResult> => {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        const { width, height } = img
        
        if (imageType === 'main') {
          // 商品主图尺寸验证
          const validSizes = [
            { width: 750, height: 1000 },
            { width: 800, height: 800 },
            { width: 800, height: 1200 }
          ]
          
          const isValidSize = validSizes.some(size => 
            size.width === width && size.height === height
          )
          
          if (!isValidSize) {
            resolve({
              isValid: false,
              errorMessage: '商品主图尺寸只能为750*1000、800*800或800*1200'
            })
          } else {
            resolve({ isValid: true })
          }
        } else {
          // SKU透底图暂无特殊尺寸要求
          resolve({ isValid: true })
        }
      }
      
      img.onerror = () => {
        resolve({
          isValid: false,
          errorMessage: '无法读取图片尺寸'
        })
      }
      
      img.src = URL.createObjectURL(file)
    })
  }

  // 文件名验证
  const validateFileName = (fileName: string, imageType: string, channel: string): ValidationResult => {
    const nameWithoutExt = fileName.replace(/\.(jpg|jpeg|png)$/i, '')
    
    if (imageType === 'main') {
      // 商品主图命名规则：严选SPUID_渠道商品ID 或 严选SPUID_渠道商品ID_数字
      const mainImagePattern = /^[A-Za-z0-9]+_[A-Za-z0-9]+(_\d+)?$/
      if (!mainImagePattern.test(nameWithoutExt)) {
        return {
          isValid: false,
          errorMessage: '商品主图命名规则：严选SPUID_渠道商品ID 或 严选SPUID_渠道商品ID_数字'
        }
      }
    } else if (imageType === 'sku') {
      // SKU透底图命名规则
      let skuPattern: RegExp
      if (channel === 'taobao') {
        skuPattern = /^[A-Za-z0-9]+_tb(_\d+)?$/
      } else if (channel === 'jd') {
        skuPattern = /^[A-Za-z0-9]+_jd(_\d+)?$/
      } else {
        skuPattern = /^[A-Za-z0-9]+(_\d+)?$/
      }
      
      if (!skuPattern.test(nameWithoutExt)) {
        let expectedFormat = '严选SKUID'
        if (channel === 'taobao') {
          expectedFormat = '严选SKUID_tb'
        } else if (channel === 'jd') {
          expectedFormat = '严选SKUID_jd'
        }
        expectedFormat += ' 或 ' + expectedFormat + '_数字'
        
        return {
          isValid: false,
          errorMessage: `SKU透底图命名规则：${expectedFormat}`
        }
      }
    }
    
    return { isValid: true }
  }

  // 文件格式验证
  const validateFileFormat = (fileName: string): ValidationResult => {
    const validFormats = /\.(jpg|jpeg|png)$/i
    if (!validFormats.test(fileName)) {
      return {
        isValid: false,
        errorMessage: '图片格式只支持jpg、jpeg、png'
      }
    }
    return { isValid: true }
  }

  // 综合验证单个文件
  const validateFile = async (file: File, imageType: string, channel: string): Promise<ValidationResult> => {
    // 格式验证
    const formatResult = validateFileFormat(file.name)
    if (!formatResult.isValid) {
      return formatResult
    }

    // 文件名验证
    const nameResult = validateFileName(file.name, imageType, channel)
    if (!nameResult.isValid) {
      return nameResult
    }

    // 尺寸验证
    const dimensionResult = await validateImageDimensions(file, imageType)
    if (!dimensionResult.isValid) {
      return dimensionResult
    }

    return { isValid: true }
  }

  // 处理文件上传
  const handleUpload = async () => {
    try {
      const values = await form.validateFields()
      const { channel, imageType } = values

      if (fileList.length === 0) {
        message.error('请选择要上传的图片')
        return
      }

      setUploading(true)
      const results: UploadResult[] = []

      for (const fileItem of fileList) {
        const file = fileItem.originFileObj || fileItem
        const result: UploadResult = {
          id: fileItem.uid,
          fileName: file.name,
          channel,
          imageType,
          status: 'error'
        }

        try {
          // 验证文件
          const validation = await validateFile(file, imageType, channel)
          
          if (!validation.isValid) {
            result.errorMessage = validation.errorMessage
            results.push(result)
            continue
          }

          // 上传文件
          const response = await uploadFile(file)
          if (response.data.code === 200) {
            result.status = 'success'
            result.fileUrl = response.data.data
          } else {
            result.errorMessage = '上传失败'
          }
        } catch (error) {
          result.errorMessage = '上传过程中发生错误'
        }

        results.push(result)
      }

      setUploadResults(results)
      setUploading(false)
      
      // 显示上传结果统计
      const successCount = results.filter(r => r.status === 'success').length
      const errorCount = results.filter(r => r.status === 'error').length
      message.info(`上传完成：成功 ${successCount} 个，失败 ${errorCount} 个`)

    } catch (error) {
      setUploading(false)
      message.error('表单验证失败')
    }
  }

  // 删除上传结果
  const handleDeleteResult = (id: string) => {
    setUploadResults(prev => prev.filter(item => item.id !== id))
  }

  // 上传结果表格列配置
  const resultColumns = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 200,
      ellipsis: true
    },
    {
      title: '归属渠道',
      dataIndex: 'channel',
      key: 'channel',
      width: 100,
      render: (channel: string) => channel === 'taobao' ? '淘系' : '京东综合'
    },
    {
      title: '图片类型',
      dataIndex: 'imageType',
      key: 'imageType',
      width: 100,
      render: (type: string) => type === 'main' ? '商品主图' : 'SKU透底图'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'success' ? 'green' : 'red'}>
          {status === 'success' ? '成功' : '失败'}
        </Tag>
      )
    },
    {
      title: '错误原因',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      ellipsis: true,
      render: (text: string) => text || '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, record: UploadResult) => (
        <Button
          type="link"
          size="small"
          icon={<DeleteOutlined />}
          onClick={() => handleDeleteResult(record.id)}
        >
          删除
        </Button>
      )
    }
  ]

  const handleCancel = () => {
    resetState()
    onCancel()
  }

  const handleOk = () => {
    if (onOk) {
      onOk(uploadResults)
    }
    resetState()
    onCancel()
  }

  // 获取上传统计信息
  const getUploadStats = () => {
    if (uploadResults.length === 0) return null

    const successCount = uploadResults.filter(r => r.status === 'success').length
    const errorCount = uploadResults.filter(r => r.status === 'error').length

    return (
      <span style={{ marginRight: 16, fontSize: 12, color: '#666' }}>
        总计: {uploadResults.length} | 成功: {successCount} | 失败: {errorCount}
      </span>
    )
  }

  return (
    <Modal
      title="图片上传"
      visible={visible}
      onCancel={handleCancel}
      width={800}
      destroyOnClose
      footer={[
        getUploadStats(),
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="ok" type="primary" onClick={handleOk}>
          确定
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          channel: 'taobao',
          imageType: 'main'
        }}
      >
        <Form.Item
          name="channel"
          label="归属渠道"
          rules={[{ required: true, message: '请选择归属渠道' }]}
        >
          <Select>
            <Option value="taobao">淘系</Option>
            <Option value="jd">京东综合</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="imageType"
          label="图片类型"
          rules={[{ required: true, message: '请选择图片类型' }]}
        >
          <Select>
            <Option value="main">商品主图</Option>
            <Option value="sku">SKU透底图</Option>
          </Select>
        </Form.Item>

        <Form.Item label="选择图片">
          <Upload
            multiple
            fileList={fileList}
            beforeUpload={() => false}
            onChange={({ fileList }) => setFileList(fileList)}
            accept=".jpg,.jpeg,.png"
          >
            <Button icon={<UploadOutlined />}>选择图片</Button>
          </Upload>
          <div className="upload-tips">
            <div className="tip-title">上传说明：</div>
            <div className="tip-item">• 支持格式：jpg、jpeg、png</div>
            {watchedValues?.imageType === 'main' && (
              <div className="tip-item">• 商品主图尺寸：750*1000 / 800*800 / 800*1200</div>
            )}
            <div className="tip-item">• 命名规则：{getNamingRuleTip()}</div>
            <div className="tip-item">• 文件命名请严格按照规则，否则会上传失败</div>
          </div>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            onClick={handleUpload}
            loading={uploading}
            disabled={fileList.length === 0}
          >
            开始上传
          </Button>
        </Form.Item>
      </Form>

      {uploadResults.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <h4>上传结果</h4>
          <Table
            columns={resultColumns}
            dataSource={uploadResults}
            rowKey="id"
            size="small"
            pagination={false}
            scroll={{ y: 300 }}
          />
        </div>
      )}
    </Modal>
  )
}

export default PictureUploadModal
