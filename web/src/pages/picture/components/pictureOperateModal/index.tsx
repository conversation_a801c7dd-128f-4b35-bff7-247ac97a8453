import React from 'react'
import { Modal, Form, Select, message, Input } from 'antd'
import { PIC_TYPE, TEAM_ID } from '../../../../interfaces'
import { loadPicSize } from '../../../../utils'
import { copyBaseSpuPicture, editSpuPicture, uploadBaseSkuPicture, uploadBaseSpuPicture } from '../../../../services'

const { Option } = Select

export enum PICTURE_OPERATE_MODE {
  COPY = 'copy',
  EDIT = 'edit',
  COPY_FROM_MAINSITE = 'copyFromMainSite'
}

const TITLE_RECORD: Record<PICTURE_OPERATE_MODE, string> = {
  [PICTURE_OPERATE_MODE.COPY]: '复制图片关联其他商品信息',
  [PICTURE_OPERATE_MODE.EDIT]: '编辑图片信息',
  [PICTURE_OPERATE_MODE.COPY_FROM_MAINSITE]: '复制为渠道商品图片'
}

export interface PictureOperateModalProps {
  isOpen: boolean
  id?: number
  mode: PICTURE_OPERATE_MODE
  teamId: TEAM_ID
  spuId: number
  channelProductId: string
  picType: PIC_TYPE
  picUrl: string
  picSize: string
  picName: string
  onCancel: () => void
}

export const PictureOperateModal: React.FC<PictureOperateModalProps> = ({
  isOpen,
  teamId,
  mode,
  channelProductId,
  picType = PIC_TYPE.NORMAL,
  onCancel,
  ...restProps
}) => {
  const [form] = Form.useForm()

  // 取消
  const handleCancel = () => {
    onCancel()
  }

  // 提交
  const handleOk = async () => {
    const values = await form.validateFields()
    let res
    switch(mode) {
      case PICTURE_OPERATE_MODE.COPY:
        // 复制图片关联其他商品信息
        res = await handleCopy(values)
        break
      case PICTURE_OPERATE_MODE.EDIT:
        // 编辑图片信息
        res = await handleEdit(values)
        break
      case PICTURE_OPERATE_MODE.COPY_FROM_MAINSITE:
        // 复制为渠道商品图片
        res = await handleCopyFromMainSite(values)
        break
      // no default
    }
    if (res && res.code === 200) {
      message.success('操作成功')
      onCancel()
    } else {
      message.error(res.message)
    }
  }

  // 主站图片复制为渠道商品主图
  const handleCopyFromMainSite = async (values: any) => {
    const { width, height } = await loadPicSize(restProps.picUrl)
    const res = await copyBaseSpuPicture({
      channelProductId: values.channelProductId,
      picType: values.picType,
      teamId: values.teamId,
      spuId: restProps.spuId,
      picSize: `${width}*${height}`,
      picName: restProps.picName,
      picUrl: restProps.picUrl
    })
    return res
  }

  // 复制图片关联其他商品信息
  const handleCopy = async (values: any) => {
    const res = await copyBaseSpuPicture({
      channelProductId: values.channelProductId,
      picType: values.picType,
      teamId: values.teamId,
      spuId: restProps.spuId,
      picSize: restProps.picSize,
      picName: restProps.picName,
      picUrl: restProps.picUrl
    })
    return res
  }

  // 编辑图片信息
  const handleEdit = async (values: any) => {
    const res = await editSpuPicture({
      channelProductId: values.channelProductId,
      picType: values.picType,
      teamId: values.teamId,
      spuId: restProps.spuId,
      picSize: restProps.picSize,
      id: restProps.id!,
      picName: restProps.picName,
      picUrl: restProps.picUrl
    })
    return res
  }

  return (
    <Modal
      open={isOpen}
      title={TITLE_RECORD[mode]}
      width={580}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <Form
        form={form}
        initialValues={{
          teamId,
          picType,
          channelProductId
        }}
        {...{
            labelCol: { span: 6 },
            wrapperCol: { span: 18 }
        }}
      >
        <Form.Item
          name="teamId"
          label="归属渠道"
          rules={[{ required: true, message: '请选择归属渠道' }]}
        >
          <Select className="sharkr-w-md">
            <Option value={TEAM_ID.TB}>淘系</Option>
            <Option value={TEAM_ID.JD}>京东综合</Option>
          </Select>
        </Form.Item>
        <Form.Item
          name="channelProductId"
          label="渠道商品ID"
          rules={[{ required: true, message: '请输入渠道商品ID' }]}
        >
          <Input allowClear className="sharkr-w-md" />
        </Form.Item>
        <Form.Item
          name="picType"
          label="选择标签"
          help="若渠道商品ID当前存在打标为该标签的图片，则将自动做替换"
          rules={[{ required: true, message: '请选择标签' }]}
        >
          <Select className="sharkr-w-md">
            <Option value={PIC_TYPE.NORMAL}>无</Option>
            <Option value={PIC_TYPE.DAILY}>日常</Option>
            <Option value={PIC_TYPE.PROMOTION}>活动</Option>
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  )
}
