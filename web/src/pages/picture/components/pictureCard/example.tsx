import React from 'react'
import { Row, Col, message } from 'antd'
import { PictureCard } from './index'
import { BasePictureBO, PIC_TYPE, TEAM_ID } from '../../../../interfaces'

/**
 * PictureCard 使用示例
 */
const PictureCardExample: React.FC = () => {
  // 模拟数据
  const mockData: BasePictureBO[] = [
    {
      baseId: 1,
      channelProductId: '8887765448776544',
      createTime: Date.now(),
      createUser: 'admin',
      picName: '商品主图_1.jpg',
      picSize: '1024*1024', // 1MB
      picType: PIC_TYPE.DAILY,
      picUrl: 'https://yanxuan-item.nosdn.127.net/a747d9078104712e5a69c4dd5c5da40e.png',
      skuId: 12345,
      spuId: 67890,
      teamId: TEAM_ID.TB,
      updateTime: Date.now(),
      updateUser: 'admin'
    },
    {
      baseId: 2,
      channelProductId: '8887765448776545',
      createTime: Date.now(),
      createUser: 'admin',
      picName: '商品主图_2.png',
      picSize: '512*1024', // 512KB
      picType: PIC_TYPE.PROMOTION,
      picUrl: 'http://mailshark.nos-jd.163yun.com/document/static/525777A1F022DAC9248D05BA131A8F8B.png',
      skuId: 12346,
      spuId: 67891,
      teamId: TEAM_ID.JD,
      updateTime: Date.now(),
      updateUser: 'admin'
    },
    {
      baseId: 3,
      channelProductId: '8887765448776546',
      createTime: Date.now(),
      createUser: 'admin',
      picName: '商品主图_3.jpg',
      picSize: '1024*1024', // 2MB
      picType: PIC_TYPE.NORMAL,
      picUrl: 'https://yanxuan-item.nosdn.127.net/a747d9078104712e5a69c4dd5c5da40e.png',
      skuId: 12347,
      spuId: 67892,
      teamId: TEAM_ID.TB,
      updateTime: Date.now(),
      updateUser: 'admin'
    }
  ]

  // 处理复制操作
  const handleCopy = (data: BasePictureBO) => {
    console.log('复制图片:', data)
    message.success(`已复制图片: ${data.picName}`)
  }

  // 处理下载操作
  const handleDownload = (data: BasePictureBO) => {
    console.log('下载图片:', data)
    message.success(`开始下载图片: ${data.picName}`)
  }

  // 处理编辑操作
  const handleEdit = (data: BasePictureBO) => {
    console.log('编辑图片:', data)
    message.info(`编辑图片信息: ${data.picName}`)
  }

  // 处理删除操作
  const handleDelete = (data: BasePictureBO) => {
    console.log('删除图片:', data)
    message.warning(`删除图片: ${data.picName}`)
  }

  return (
    <div style={{ padding: 24 }}>
      <h2>PictureCard 组件示例</h2>
      <p>展示不同类型的图片卡片，包括日常、大促和普通类型</p>
      
      <Row gutter={[16, 16]}>
        {mockData.map((item) => (
          <Col key={item.baseId} xs={24} sm={12} md={8} lg={6}>
            <PictureCard
              data={item}
              onCopy={handleCopy}
              onDownload={handleDownload}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          </Col>
        ))}
      </Row>

      <div style={{ marginTop: 32 }}>
        <h3>功能说明：</h3>
        <ul>
          <li>点击图片可以预览大图</li>
          <li>鼠标悬停在图片上会显示操作按钮</li>
          <li>点击操作按钮可以进行复制、下载、编辑、删除操作</li>
          <li>不同图片类型会显示不同的标识（日常、大促）</li>
          <li>显示图片尺寸、渠道商品ID、团队信息等</li>
        </ul>
      </div>
    </div>
  )
}

export default PictureCardExample
