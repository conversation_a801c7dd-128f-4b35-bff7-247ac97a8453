.picture-card-wrapper {
  .picture-card {
    width: 200px;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f4f4f4;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      // transform: translateY(-2px);
    }

    .ant-card-body {
      padding: 12px;
    }

    .picture-cover {
      position: relative;
      padding-top: 10px;
      padding: 10px 10px 0;
      text-align: center;
      // height: 230px;
      // height: 200px;
      overflow: hidden;
      background: #f5f5f5;

      .picture-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
        cursor: pointer;
        transition: transform 0.3s ease;

        &:hover {
          // transform: scale(1.05);
        }
      }

      .picture-size {
        line-height: 30px;
        color: #666;
      }

      .picture-actions {
        position: absolute;
        bottom: 8px;
        right: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;

        .more-actions-btn {
          width: 32px;
          height: 32px;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          color: white;
          font-size: 16px;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
          }
        }
      }

      &:hover .picture-actions {
        opacity: 1;
      }
    }

    .picture-info {
      display: flex;
      align-items: center;
      justify-content: center;
      .info-name {
        // width: 124px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        // font-size: 12px;
        color: #333;
      }
    }
  }
}

// 图片预览Modal样式
.picture-preview-modal {
  .ant-modal-body {
    padding: 0;
    text-align: center;
  }

  .preview-info {
    padding: 16px;
    background: #fafafa;
    border-top: 1px solid #f0f0f0;

    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      color: #333;
    }

    p {
      margin: 0;
      font-size: 12px;
      color: #666;
    }
  }
}

// 操作菜单样式
.ant-dropdown-menu {
  .ant-dropdown-menu-item {
    &.ant-dropdown-menu-item-danger {
      color: #ff4d4f;

      &:hover {
        background-color: #fff2f0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .picture-card-wrapper {
    .picture-card {
      width: 100%;
      max-width: 280px;
    }
  }
}

// 加载状态样式
.picture-card-loading {
  .picture-cover {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;

    .ant-spin {
      color: #1890ff;
    }
  }
}

// 错误状态样式
.picture-card-error {
  .picture-cover {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    color: #999;

    .error-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }

    .error-text {
      font-size: 12px;
    }
  }
}

// 选中状态样式
.picture-card-selected {
  .picture-card {
    border: 2px solid #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

// 禁用状态样式
.picture-card-disabled {
  .picture-card {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .picture-cover {
      .picture-image {
        cursor: not-allowed;

        &:hover {
          transform: none;
        }
      }

      .picture-actions {
        display: none;
      }
    }
  }
}
