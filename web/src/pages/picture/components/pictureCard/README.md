# PictureCard 图片卡片组件

## 概述
PictureCard 是一个用于展示图片信息的卡片组件，基于 BasePictureBO 接口设计，提供图片预览、操作菜单等功能。

## 功能特性

### 1. 图片展示
- **图片预览**: 显示 picUrl 对应的图片，支持点击预览大图
- **图片信息**: 显示图片尺寸、类型、渠道商品ID、团队信息
- **响应式设计**: 支持不同屏幕尺寸的适配

### 2. 图片类型显示
- **普通底图** (PIC_TYPE.NORMAL = 0): 不显示类型标识
- **日常** (PIC_TYPE.DAILY = 1): 显示"日常"标识
- **大促** (PIC_TYPE.PROMOTION = 2): 显示"大促"标识

### 3. 交互功能
- **图片预览**: 点击图片弹出预览浮层
- **操作菜单**: 鼠标悬停显示操作按钮，包含四个操作：
  - 复制图片（默认复制图片URL到剪贴板）
  - 下载图片（默认下载图片文件）
  - 编辑图片信息（需要传入回调函数）
  - 删除（弹出确认对话框）

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| data | 图片数据 | BasePictureBO | - | 是 |
| onCopy | 复制图片回调 | (data: BasePictureBO) => void | - | 否 |
| onDownload | 下载图片回调 | (data: BasePictureBO) => void | - | 否 |
| onEdit | 编辑图片信息回调 | (data: BasePictureBO) => void | - | 否 |
| onDelete | 删除图片回调 | (data: BasePictureBO) => void | - | 否 |

### BasePictureBO 接口

```typescript
interface BasePictureBO {
  baseId: number;              // 底图id
  channelProductId: string;    // 渠道商品 sku id
  createTime: number;          // 创建时间
  createUser: string;          // 创建用户
  picName: string;             // 图片名称
  picSize: number;             // 图片大小（字节）
  picType: PIC_TYPE;           // 图片类型
  picUrl: string;              // 图片URL
  skuId: number;               // SKU ID
  spuId: number;               // SPU ID
  teamId: TEAM_ID;             // 团队ID
  updateTime: number;          // 更新时间
  updateUser: string;          // 更新用户
}
```

## 使用方式

### 基本用法

```tsx
import React from 'react'
import { PictureCard } from '@/pages/picture/components'
import { BasePictureBO } from '@/interfaces'

const MyComponent: React.FC = () => {
  const pictureData: BasePictureBO = {
    baseId: 1,
    channelProductId: '8887765448776544',
    createTime: Date.now(),
    createUser: 'admin',
    picName: '商品主图.jpg',
    picSize: 1024 * 1024,
    picType: PIC_TYPE.DAILY,
    picUrl: 'https://example.com/image.jpg',
    skuId: 12345,
    spuId: 67890,
    teamId: TEAM_ID.TB,
    updateTime: Date.now(),
    updateUser: 'admin'
  }

  return (
    <PictureCard data={pictureData} />
  )
}
```

### 带回调函数的用法

```tsx
import React from 'react'
import { message } from 'antd'
import { PictureCard } from '@/pages/picture/components'

const MyComponent: React.FC = () => {
  const handleCopy = (data: BasePictureBO) => {
    // 自定义复制逻辑
    console.log('复制图片:', data)
    message.success('图片已复制')
  }

  const handleDownload = (data: BasePictureBO) => {
    // 自定义下载逻辑
    console.log('下载图片:', data)
  }

  const handleEdit = (data: BasePictureBO) => {
    // 打开编辑弹窗
    console.log('编辑图片:', data)
  }

  const handleDelete = (data: BasePictureBO) => {
    // 删除图片
    console.log('删除图片:', data)
  }

  return (
    <PictureCard
      data={pictureData}
      onCopy={handleCopy}
      onDownload={handleDownload}
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  )
}
```

### 网格布局使用

```tsx
import React from 'react'
import { Row, Col } from 'antd'
import { PictureCard } from '@/pages/picture/components'

const PictureGrid: React.FC = () => {
  return (
    <Row gutter={[16, 16]}>
      {pictureList.map((item) => (
        <Col key={item.baseId} xs={24} sm={12} md={8} lg={6}>
          <PictureCard
            data={item}
            onCopy={handleCopy}
            onDownload={handleDownload}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        </Col>
      ))}
    </Row>
  )
}
```

## 样式定制

组件提供了丰富的CSS类名，可以进行样式定制：

```scss
.picture-card-wrapper {
  .picture-card {
    // 卡片样式
  }
  
  .picture-cover {
    // 图片封面样式
  }
  
  .picture-info {
    // 信息区域样式
  }
}
```

## 注意事项

1. **图片加载**: 组件提供了图片加载失败的fallback处理
2. **响应式**: 组件支持响应式布局，在不同屏幕尺寸下自适应
3. **性能**: 大量图片时建议使用虚拟滚动或分页加载
4. **权限**: 操作按钮的显示可以根据用户权限进行控制
5. **错误处理**: 建议在回调函数中添加适当的错误处理逻辑

## 扩展功能

### 1. 批量选择
可以扩展组件支持批量选择功能：

```tsx
interface PictureCardProps {
  // ... 其他props
  selectable?: boolean
  selected?: boolean
  onSelect?: (data: BasePictureBO, selected: boolean) => void
}
```

### 2. 拖拽排序
可以集成拖拽库实现图片排序功能

### 3. 懒加载
对于大量图片，可以实现图片懒加载功能

## 文件结构

```
web/src/pages/picture/components/pictureCard/
├── PictureCard.tsx      # 主组件
├── PictureCard.scss     # 样式文件
├── index.ts            # 导出文件
├── example.tsx         # 使用示例
└── README.md           # 说明文档
```
