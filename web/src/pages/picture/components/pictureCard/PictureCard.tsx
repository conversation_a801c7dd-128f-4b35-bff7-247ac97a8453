import React, { useState } from 'react'
import { Card, Image, Dropdown, Menu, Modal, message, Tag } from 'antd'
import { MoreOutlined, CopyOutlined, DownloadOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { BasePictureBO, PIC_TYPE, TEAM_ID } from '../../../../interfaces'
import './PictureCard.scss'

export interface PictureCardProps {
  /** 图片数据 */
  data: BasePictureBO
  /** 复制图片回调 */
  onCopy?: (data: BasePictureBO) => void
  /** 下载图片回调 */
  onDownload?: (data: BasePictureBO) => void
  /** 编辑图片信息回调 */
  onEdit?: (data: BasePictureBO) => void
  /** 删除图片回调 */
  onDelete?: (data: BasePictureBO) => void
}

const PictureCard: React.FC<PictureCardProps> = ({
  data,
  onCopy,
  onDownload,
  onEdit,
  onDelete
}) => {
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false)
  const [hovering, setHovering] = useState(false)

  // 获取图片类型显示文本
  const getPicTypeText = (picType: PIC_TYPE) => {
    switch (picType) {
      case PIC_TYPE.DAILY:
        return '日常'
      case PIC_TYPE.PROMOTION:
        return '大促'
      case PIC_TYPE.NORMAL:
      default:
        return ''
    }
  }

  // 获取团队名称
  const getTeamName = (teamId: TEAM_ID) => {
    switch (teamId) {
      case TEAM_ID.TB:
        return '淘宝'
      case TEAM_ID.JD:
        return '京东综合'
      default:
        return '未知'
    }
  }

  // 格式化图片尺寸
  const formatPicSize = (size: number) => {
    if (size < 1024) {
      return `${size}B`
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(1)}KB`
    } else {
      return `${(size / (1024 * 1024)).toFixed(1)}MB`
    }
  }

  // 处理复制操作
  const handleCopy = () => {
    if (onCopy) {
      onCopy(data)
    } else {
      // 默认复制图片URL到剪贴板
      navigator.clipboard.writeText(data.picUrl).then(() => {
        message.success('图片链接已复制到剪贴板')
      }).catch(() => {
        message.error('复制失败')
      })
    }
  }

  // 处理下载操作
  const handleDownload = () => {
    if (onDownload) {
      onDownload(data)
    } else {
      // 默认下载图片
      const link = document.createElement('a')
      link.href = data.picUrl
      link.download = data.picName || `image_${data.baseId}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  // 处理编辑操作
  const handleEdit = () => {
    if (onEdit) {
      onEdit(data)
    }
  }

  // 处理删除操作
  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这张图片吗？删除后无法恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        if (onDelete) {
          onDelete(data)
        }
      }
    })
  }

  // 操作菜单项
  const menuItems = [
    {
      key: 'copy',
      icon: <CopyOutlined />,
      label: '复制图片',
      onClick: handleCopy
    },
    {
      key: 'download',
      icon: <DownloadOutlined />,
      label: '下载图片',
      onClick: handleDownload
    },
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: '编辑图片信息',
      onClick: handleEdit
    },
    {
      type: 'divider' as const
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      onClick: handleDelete,
      danger: true
    }
  ]

  return (
    <div className="picture-card-wrapper">
      <Card
        hoverable
        className="picture-card"
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
        cover={
          <div className="picture-cover">
            <Image
              width={180}
              height={180}
              src={data.picUrl}
              alt={data.picName}
              // preview={false}
              // onClick={() => setImagePreviewVisible(true)}
              className="picture-image"
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
            <div className="picture-size">{data.picSize}</div>
            
            {/* 悬停时显示的操作按钮 */}
            {hovering && (
              <div className="picture-actions">
                <Dropdown menu={{ items: menuItems }} trigger={['click']} placement="bottomRight">
                  <div className="more-actions-btn">
                    <MoreOutlined />
                  </div>
                </Dropdown>
              </div>
            )}
          </div>
        }
      >
        <div className="picture-info">          
          {/* 图片类型 */}
          {data.picType === PIC_TYPE.DAILY && (
            <Tag color="blue">日常</Tag>
          )}
          {data.picType === PIC_TYPE.PROMOTION && (
            <Tag color="gold">活动</Tag>
          )}
           <span className="info-name">{data.channelProductId}</span>
        </div>
      </Card>

      {/* 图片预览Modal */}
      <Modal
        open={imagePreviewVisible}
        footer={null}
        onCancel={() => setImagePreviewVisible(false)}
        width="auto"
        centered
        className="picture-preview-modal"
      >
        <Image
          src={data.picUrl}
          alt={data.picName}
          style={{ maxWidth: '100%', maxHeight: '80vh' }}
        />
        {/* <div className="preview-info">
          <h4>{data.picName}</h4>
          <div className="preview-details">
            <span>尺寸: {formatPicSize(data.picSize)}</span>
            <span>类型: {getPicTypeText(data.picType)}</span>
            <span>渠道商品ID: {data.channelProductId}</span>
          </div>
        </div> */}
      </Modal>
    </div>
  )
}

export default PictureCard
