/**
 * @Description: SPU主图列表弹窗组件，用于展示指定SPU的主图列表，支持图片的查看、复制、下载、编辑和删除操作
 * @Author: AI Assistant
 * @Date: 2025-07-10
 */
import React, { useEffect, useState } from 'react'
import { Modal, Spin, message, Empty } from 'antd'
import { BasePictureBO, PIC_TYPE, SpuPictureListQueryParams, TEAM_ID, TEAM_NAME_RECORD } from '../../../../interfaces'
import { deleteSpuPicture, getSpuPictureList } from '../../../../services/pictureService'
import { PictureCard } from '../pictureCard'
import { downloadImageByUrl } from '../../../../services/uploadService'
import './index.scss'
import { PICTURE_OPERATE_MODE, PictureOperateModal, PictureOperateModalProps } from '../pictureOperateModal'

export interface PicturePrimaryListModalProps {
  /** 弹窗是否可见 */
  isOpen: boolean
  /** 关闭弹窗回调 */
  onCancel: () => void
  /** SPU ID */
  spuId?: number
  /** 团队 ID */
  teamId?: TEAM_ID
  /** SPU 名称 */
  spuName?: string
}

export const PicturePrimaryListModal: React.FC<PicturePrimaryListModalProps> = ({
  isOpen,
  onCancel,
  spuId,
  teamId,
  spuName
}) => {
  const [loading, setLoading] = useState(false)
  const [pictureList, setPictureList] = useState<BasePictureBO[]>([])
  const [optModalParams, setOptModalParams] = useState<Omit<PictureOperateModalProps, 'isOpen' | 'onCancel'>>()

  // 获取图片列表
  const fetchPictureList = async () => {
    if (!spuId || !teamId) {
      return
    }

    setLoading(true)
    try {
      const params: SpuPictureListQueryParams = {
        spuId,
        teamId
      }
      
      const response = await getSpuPictureList(params)
      
      if (response && response.code === 200) {
        setPictureList(response.data.pictureList || [])
      } else {
        message.error('获取图片列表失败')
        setPictureList([])
      }
    } catch (error) {
      console.error('获取图片列表失败:', error)
      message.error('获取图片列表失败')
      setPictureList([])
    } finally {
      setLoading(false)
    }
  }

  // 当弹窗打开时获取数据
  useEffect(() => {
    if (isOpen) {
      fetchPictureList()
    }
  }, [isOpen, spuId, teamId])


  // 复制图片
  const handleCopy = (data: BasePictureBO) => {
    setOptModalParams({
      mode: PICTURE_OPERATE_MODE.COPY,
      teamId: teamId!,
      spuId: spuId!,
      channelProductId: '',
      picType: data.picType,
      picUrl: data.picUrl,
      picSize: data.picSize,
      picName: data.picName
    })
  }

  // 处理下载操作
  const handleDownload = (data: BasePictureBO) => {
    downloadImageByUrl(data.picUrl, data.channelProductId)
  }

  
  // 编辑图片
  const handleEdit = (data: BasePictureBO) => {
    setOptModalParams({
      mode: PICTURE_OPERATE_MODE.EDIT,
      id: data.baseId,
      teamId: teamId!,
      spuId: spuId!,
      channelProductId: data.channelProductId,
      picType: data.picType,
      picUrl: data.picUrl,
      picSize: data.picSize,
      picName: data.picName
    })
  }

  // 删除
  const handleDelete = (data: BasePictureBO) => {
    Modal.confirm({
      title: '操作提示',
      content: '删除之后不可恢复，是否确认删除？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await deleteSpuPicture({
          baseId: data.baseId,
          spuId: data.spuId
        })
        if (res && res.code === 200) {
          message.success('操作成功')
          fetchPictureList()
        }
      }
    })
  }

  // 关闭弹窗
  const handleCancel = () => {
    setPictureList([])
    onCancel()
  }

  /**
   * @description: 根据图片类型获取标签配置
   * @param {PIC_TYPE} picType 图片类型
   * @returns {object | undefined} 标签配置对象
   */
  const getTag = (picType: PIC_TYPE): { color: string; text: string } | undefined => {
    let tag;
    switch (picType) {
      case PIC_TYPE.DAILY:
        tag = {
          color: 'blue',
          text: '日常'
        };
        break;
      case PIC_TYPE.PROMOTION:
        tag = {
          color: 'orange',
          text: '活动'
        };
        break;
      // no default
    }

    return tag
  }

  return (
    <Modal
      centered
      title={`【${teamId ? TEAM_NAME_RECORD[teamId] : ''}】${spuId} ${spuName}`}
      open={isOpen}
      onCancel={handleCancel}
      footer={null}
      width={900}
      className="picture-primary-list-modal"
      destroyOnClose
    >
      <div className="picture-list-content">
        {loading ? (
          <div className="loading-container">
            <Spin size="large" />
            <p>加载中...</p>
          </div>
        ) : pictureList.length > 0 ? (
          <div className="picture-list-body">
            {pictureList.map((picture) => (
              <PictureCard
                key={picture.baseId}
                title={picture.channelProductId}
                subTitle={picture.picSize}
                picUrl={picture.picUrl}
                tag={getTag(picture.picType)}
                menus={[
                  {
                    key: 'copy',
                    label: '复制图片',
                    onClick: () => handleCopy(picture)
                  },
                  {
                    key: 'download',
                    label: '下载图片',
                    onClick: () => handleDownload(picture)
                  },
                  {
                    key: 'edit',
                    label: '编辑图片信息',
                    onClick: () => handleEdit(picture)
                  },
                  {
                    key: 'delete',
                    label: '删除',
                    onClick: () => handleDelete(picture),
                  }
                ]}
              />
            ))}
          </div>
        ) : (
          <Empty
            description="暂无图片数据"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </div>
      {
        optModalParams && (
          <PictureOperateModal
            isOpen
            {...optModalParams}
            onCancel={() => setOptModalParams(undefined)}
          />
        )
      }
    </Modal>
  )
}
