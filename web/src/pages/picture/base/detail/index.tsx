import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { 
  Card, 
  Spin, 
  message, 
  Breadcrumb, 
  Row, 
  Col, 
  Tabs, 
  Button, 
  Table, 
  Empty,
  Space,
  Typography
} from 'antd'
import { ArrowLeftOutlined, UploadOutlined } from '@ant-design/icons'
import { SpuDetailBO, BasePictureBO, TEAM_ID, TEAM_NAME_RECORD } from '../../../../interfaces'
import { getSpuDetail } from '../../../../services/pictureService'
import { PictureCard } from '../../components/pictureCard'
import { PictureUploadModal } from '../../components/pictureUploadModal'
import './index.scss'

const { Title, Text } = Typography
const { TabPane } = Tabs

interface RouteParams {
  spuId: string
}

const PictureBaseDetail: React.FC = () => {
  const { spuId } = useParams<RouteParams>()
  const navigate = useNavigate()
  
  const [loading, setLoading] = useState(false)
  const [detailData, setDetailData] = useState<SpuDetailBO | null>(null)
  const [activeTeamId, setActiveTeamId] = useState<TEAM_ID>(TEAM_ID.TB)
  const [uploadModalVisible, setUploadModalVisible] = useState(false)

  // 获取详情数据
  const fetchDetailData = async () => {
    if (!spuId) {
      message.error('缺少SPU ID参数')
      return
    }

    setLoading(true)
    try {
      const response = await getSpuDetail({ spuId: Number(spuId) })
      
      if (response && response.code === 200) {
        setDetailData(response.data)
      } else {
        message.error('获取商品详情失败')
      }
    } catch (error) {
      console.error('获取商品详情失败:', error)
      message.error('获取商品详情失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDetailData()
  }, [spuId])

  // 返回列表页
  const handleGoBack = () => {
    navigate('/picture/base')
  }

  // 处理复制为渠道商品图片
  const handleCopyAsChannelPicture = (data: BasePictureBO) => {
    console.log('复制为渠道商品图片:', data)
    message.success('复制为渠道商品图片功能待实现')
  }

  // 处理下载图片
  const handleDownloadPicture = (data: BasePictureBO) => {
    const link = document.createElement('a')
    link.href = data.picUrl
    link.download = data.picName || `image_${data.baseId}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    message.success('开始下载图片')
  }

  // 处理复制图片
  const handleCopyPicture = (data: BasePictureBO) => {
    navigator.clipboard.writeText(data.picUrl).then(() => {
      message.success('图片链接已复制到剪贴板')
    }).catch(() => {
      message.error('复制失败')
    })
  }

  // 处理编辑图片信息
  const handleEditPicture = (data: BasePictureBO) => {
    console.log('编辑图片信息:', data)
    message.info('编辑图片信息功能待实现')
  }

  // 处理删除图片
  const handleDeletePicture = (data: BasePictureBO) => {
    console.log('删除图片:', data)
    message.warning('删除图片功能待实现')
  }

  // 打开上传弹窗
  const handleOpenUploadModal = () => {
    setUploadModalVisible(true)
  }

  // 关闭上传弹窗
  const handleCloseUploadModal = () => {
    setUploadModalVisible(false)
  }

  // 上传成功回调
  const handleUploadSuccess = () => {
    message.success('图片上传成功')
    fetchDetailData() // 重新获取数据
  }

  // 根据团队ID过滤商品主图
  const getFilteredBasePictureList = (teamId: TEAM_ID): BasePictureBO[] => {
    if (!detailData?.basePictureList) return []
    return detailData.basePictureList.filter(item => item.teamId === teamId)
  }

  // 历史操作记录表格列定义
  const historyColumns = [
    {
      title: '操作类型',
      dataIndex: 'operationType',
      key: 'operationType',
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      key: 'operationTime',
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
    },
    {
      title: '操作描述',
      dataIndex: 'description',
      key: 'description',
    },
  ]

  if (loading) {
    return (
      <div className="picture-base-detail-loading">
        <Spin size="large" />
        <p>加载中...</p>
      </div>
    )
  }

  if (!detailData) {
    return (
      <div className="picture-base-detail-empty">
        <Empty description="暂无数据" />
        <Button type="primary" onClick={handleGoBack}>
          返回列表
        </Button>
      </div>
    )
  }

  return (
    <div className="picture-base-detail">
      {/* 面包屑导航 */}
      <div className="detail-header">
        <Breadcrumb>
          <Breadcrumb.Item>
            <Button 
              type="link" 
              icon={<ArrowLeftOutlined />} 
              onClick={handleGoBack}
            >
              商品底图管理
            </Button>
          </Breadcrumb.Item>
          <Breadcrumb.Item>商品详情</Breadcrumb.Item>
        </Breadcrumb>
      </div>

      {/* 商品基本信息 */}
      <Card title="商品基本信息" className="detail-section">
        <Row gutter={[24, 16]}>
          <Col span={6}>
            <Text strong>商品ID：</Text>
            <Text>{detailData.spuId}</Text>
          </Col>
          <Col span={6}>
            <Text strong>商品名称：</Text>
            <Text>{detailData.name}</Text>
          </Col>
          <Col span={6}>
            <Text strong>归属BU：</Text>
            <Text>{detailData.buName} / {detailData.secondBuName}</Text>
          </Col>
          <Col span={6}>
            <Text strong>商品类目：</Text>
            <Text>{detailData.categoryName}</Text>
          </Col>
        </Row>

        {/* 主站商品详情图 */}
        <div className="picture-section">
          <Title level={4}>主站商品详情图</Title>
          <div className="picture-list-body">
            {detailData.icPictureList && detailData.icPictureList.length > 0 ? (
              detailData.icPictureList.map((picUrl, index) => (
                <PictureCard
                  key={`ic-${index}`}
                  data={{
                    baseId: index,
                    picUrl,
                    picName: `主站详情图_${index + 1}`,
                    picSize: '0',
                    picType: 0,
                    channelProductId: '',
                    createTime: 0,
                    createUser: '',
                    skuId: 0,
                    spuId: detailData.spuId,
                    teamId: TEAM_ID.TB,
                    updateTime: 0,
                    updateUser: ''
                  }}
                  onCopy={handleCopyAsChannelPicture}
                  onDownload={handleDownloadPicture}
                />
              ))
            ) : (
              <Empty description="暂无主站商品详情图" />
            )}
          </div>
        </div>

        {/* 主站SKU透底图 */}
        <div className="picture-section">
          <Title level={4}>主站SKU透底图</Title>
          <div className="picture-list-body">
            {detailData.icSkuPictureList && detailData.icSkuPictureList.length > 0 ? (
              detailData.icSkuPictureList.map((picUrl, index) => (
                <PictureCard
                  key={`ic-sku-${index}`}
                  data={{
                    baseId: index,
                    picUrl,
                    picName: `主站SKU透底图_${index + 1}`,
                    picSize: '0',
                    picType: 0,
                    channelProductId: '',
                    createTime: 0,
                    createUser: '',
                    skuId: 0,
                    spuId: detailData.spuId,
                    teamId: TEAM_ID.TB,
                    updateTime: 0,
                    updateUser: ''
                  }}
                />
              ))
            ) : (
              <Empty description="暂无主站SKU透底图" />
            )}
          </div>
        </div>
      </Card>

      {/* 渠道商品主图区域 */}
      <Card
        title={
          <div className="section-header">
            <span>渠道商品主图</span>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={handleOpenUploadModal}
            >
              上传图片
            </Button>
          </div>
        }
        className="detail-section"
      >
        <Tabs
          activeKey={activeTeamId.toString()}
          onChange={(key) => setActiveTeamId(Number(key) as TEAM_ID)}
        >
          <TabPane tab={TEAM_NAME_RECORD[TEAM_ID.TB]} key={TEAM_ID.TB.toString()}>
            <div className="picture-list-body">
              {getFilteredBasePictureList(TEAM_ID.TB).length > 0 ? (
                getFilteredBasePictureList(TEAM_ID.TB).map((picture) => (
                  <PictureCard
                    key={picture.baseId}
                    data={picture}
                    onCopy={handleCopyPicture}
                    onDownload={handleDownloadPicture}
                    onEdit={handleEditPicture}
                    onDelete={handleDeletePicture}
                  />
                ))
              ) : (
                <Empty description="暂无淘宝渠道商品主图" />
              )}
            </div>
          </TabPane>
          <TabPane tab={TEAM_NAME_RECORD[TEAM_ID.JD]} key={TEAM_ID.JD.toString()}>
            <div className="picture-list-body">
              {getFilteredBasePictureList(TEAM_ID.JD).length > 0 ? (
                getFilteredBasePictureList(TEAM_ID.JD).map((picture) => (
                  <PictureCard
                    key={picture.baseId}
                    data={picture}
                    onCopy={handleCopyPicture}
                    onDownload={handleDownloadPicture}
                    onEdit={handleEditPicture}
                    onDelete={handleDeletePicture}
                  />
                ))
              ) : (
                <Empty description="暂无京东渠道商品主图" />
              )}
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* SKU透底图区域 */}
      <Card title="SKU透底图" className="detail-section">
        <div className="picture-list-body">
          {detailData.skuPictureList && detailData.skuPictureList.length > 0 ? (
            detailData.skuPictureList.map((picture) => (
              <PictureCard
                key={picture.baseId}
                data={picture}
                onDownload={handleDownloadPicture}
                onDelete={handleDeletePicture}
              />
            ))
          ) : (
            <Empty description="暂无SKU透底图" />
          )}
        </div>
      </Card>

      {/* 历史操作记录区域 */}
      <Card title="历史操作记录" className="detail-section">
        <Table
          columns={historyColumns}
          dataSource={[]}
          pagination={false}
          locale={{ emptyText: '暂无操作记录' }}
        />
      </Card>

      {/* 上传图片弹窗 */}
      <PictureUploadModal
        isOpen={uploadModalVisible}
        onCancel={handleCloseUploadModal}
        onOk={handleUploadSuccess}
        teamId={activeTeamId}
        imageType="main"
      />
    </div>
  )
}

export default PictureBaseDetail
