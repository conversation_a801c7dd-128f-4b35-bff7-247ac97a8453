.picture-base-detail {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .detail-header {
    margin-bottom: 24px;
    background: white;
    padding: 16px 24px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-breadcrumb {
      margin: 0;
    }

    .ant-btn-link {
      padding: 0;
      height: auto;
      line-height: 1.5;
      color: #1890ff;

      &:hover {
        color: #40a9ff;
      }
    }
  }

  .detail-section {
    margin-bottom: 24px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .ant-card-body {
      padding: 24px;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
  }

  .picture-section {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;

    &:first-child {
      margin-top: 24px;
      padding-top: 0;
      border-top: none;
    }

    .ant-typography-h4 {
      margin-bottom: 16px;
      color: #333;
      font-weight: 600;
    }
  }

  .picture-list-body {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-start;

    .ant-empty {
      width: 100%;
      padding: 40px 20px;
      text-align: center;
    }
  }

  // 渠道商品主图区域样式
  .ant-tabs {
    .ant-tabs-tab {
      font-size: 14px;
      font-weight: 500;
    }

    .ant-tabs-content-holder {
      padding-top: 16px;
    }
  }

  // 基本信息区域样式
  .ant-row {
    .ant-col {
      margin-bottom: 16px;

      .ant-typography {
        margin: 0;
        line-height: 1.5;

        &.ant-typography-strong {
          color: #666;
          margin-right: 8px;
        }
      }
    }
  }

  // 表格样式
  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #333;
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
    }
  }
}

// 加载状态样式
.picture-base-detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #666;

  .ant-spin {
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

// 空数据状态样式
.picture-base-detail-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px;

  .ant-empty {
    margin-bottom: 24px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .picture-base-detail {
    .picture-list-body {
      gap: 12px;
    }

    .detail-section {
      .ant-card-body {
        padding: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .picture-base-detail {
    padding: 16px;

    .detail-header {
      padding: 12px 16px;
    }

    .detail-section {
      margin-bottom: 16px;

      .ant-card-body {
        padding: 12px;
      }
    }

    .picture-section {
      margin-top: 24px;
      padding-top: 16px;
    }

    .picture-list-body {
      gap: 8px;
      justify-content: center;
    }

    // 基本信息在移动端垂直排列
    .ant-row {
      .ant-col {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 12px;
      }
    }

    // Tab在移动端的样式调整
    .ant-tabs {
      .ant-tabs-nav {
        margin-bottom: 12px;
      }

      .ant-tabs-content-holder {
        padding-top: 12px;
      }
    }
  }
}

// 图片卡片在详情页的特殊样式
.picture-base-detail {
  .picture-card-wrapper {
    .picture-card {
      width: 240px;
      
      @media (max-width: 768px) {
        width: 200px;
      }
    }
  }
}

// 面包屑导航样式增强
.picture-base-detail {
  .detail-header {
    .ant-breadcrumb {
      .ant-breadcrumb-separator {
        color: #d9d9d9;
      }

      .ant-breadcrumb-link {
        color: #666;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}

// 卡片标题区域样式
.picture-base-detail {
  .detail-section {
    .section-header {
      .ant-btn {
        height: 32px;
        font-size: 14px;
      }
    }
  }
}
