/* eslint-disable @fe-sdk/comments/require-interface-comment */
// 图片底图相关接口定义

export enum UPLOAD_IMAGE_TYPE {
  // 渠道商品主图
  MAIN = 'main',
  // SKU透底图
  SKU = 'sku'
}

export enum PIC_TYPE {
  // 普通底图
  NORMAL = 0,
  // 日常
  DAILY = 1,
  // 大促
  PROMOTION = 2
}

export enum TEAM_ID {
  // 淘宝
  TB = 2,
  // 京东综合
  JD = 3
}

export const TEAM_NAME_RECORD: Record<TEAM_ID, string> = {
  [TEAM_ID.TB]: '淘宝',
  [TEAM_ID.JD]: '京东'
}

export interface BasePicQueryVO {
  channelProductId?: string
  endTime?: number
  itemName?: string
  operator?: string
  secondBuIds?: number[]
  spuId?: number
  startTime?: number
}

export interface PageVO<T> {
    paginationVO: PaginationVO; // 分页信息
    result: T[]; // 列表数据
}

export interface PaginationVO {
    page: number; // 当前页
    size: number; // 页大小
    total: number; // 结果总数
    totalPage: number; // 总页数
}

export interface BasePictureListBO {
  buId: number
  buName: string
  name: string
  pictureStatisticsList: BasePictureStatisticsBO[]
  secondBuId: number
  secondBuName: string
  skuPicCount: number // sku图片数量
  spuId: number
  updateTime: number
  updator: string
}

export interface BasePictureStatisticsBO {
    pictureList: BasePictureBO[];
    teamId: TEAM_ID;
    teamName: string;
    totalCount: number;
}

export interface BasePictureBO {
    baseId: number; // 底图id
    channelProductId: string; // 渠道商品 sku id
    createTime: number;
    createUser: string;
    picName: string;
    picSize: string;
    picType: PIC_TYPE; // 0 普通底图 1 日常 2 大促
    picUrl: string;
    skuId: number;
    spuId: number;
    teamId: TEAM_ID;
    updateTime: number;
    updateUser: string;
}

// 用于Antd分页组件的接口
export interface IAntDPaginationProps {
    current: number;
    pageSize: number;
    total?: number;
}

// 查询参数接口（用于组件内部状态管理）
export interface IPictureQueryProps {
    pageNo: number;
    pageSize: number;
    channelProductId?: string;
    endTime?: number;
    itemName?: string;
    operator?: string;
    secondBuIds?: number[];
    spuId?: number;
    startTime?: number;
}


export interface SkuPictureUploadVO {
  // channelProductId?: string // 渠道商品 sku id
  // createUser: string
  picName: string
  picSize: string
  picType: PIC_TYPE // 0 普通底图 1 日常 2 大促
  picUrl: string
  skuId: string
  teamId?: TEAM_ID
}

export interface SpuPictureUploadVO {
  channelProductId: string // 渠道商品 sku id
  // createUser: string
  picName: string
  picSize: string
  picType: PIC_TYPE // 0 普通底图 1 日常 2 大促
  picUrl: string
  spuId: number
  teamId: TEAM_ID
}

// 图片上传结果接口
export interface PictureUploadResult {
  success: boolean
  data?: any // 成功时的响应数据
  error?: any // 失败时的错误信息
  param: SpuPictureUploadVO | SkuPictureUploadVO // 原始上传参数
}

// SPU图片列表查询参数
export interface SpuPictureListQueryParams {
  teamId: number
  spuId: number
}

// SPU详情查询参数
export interface SpuDetailQueryParams {
  spuId: number
}

// SPU详情数据结构
export interface SpuDetailBO {
  basePictureList: BasePictureBO[]      // 商品主图列表
  buId: number                          // 一级BU ID
  buName: string                        // 一级BU名称
  categoryName: string                  // 分类名称
  icPictureList: string[]               // IC图片列表（URL数组）
  icSkuPictureList: string[]            // IC SKU图片列表（URL数组）
  name: string                          // 商品名称
  secondBuId: number                    // 二级BU ID
  secondBuName: string                  // 二级BU名称
  skuPictureList: BasePictureBO[]       // SKU图片列表
  spuId: number                         // SPU ID
}

// 渠道主图编辑参数
export interface SpuPictureEditVO {
  channelProductId: string // 渠道商品 sku id
  // createUser: string
  id: number
  picName: string
  picSize: string
  picType: PIC_TYPE // 0 普通底图 1 日常 2 大促
  picUrl: string
  spuId: number
  teamId: TEAM_ID
}
